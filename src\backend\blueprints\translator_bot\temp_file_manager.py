import os
import tempfile
import shutil
import uuid
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from flask import current_app
from werkzeug.utils import secure_filename
import atexit


class TemporaryFileManager:
    """
    Manages temporary directories and files for the translator service.
    Provides automatic cleanup, session management, and secure file handling.
    """
    
    def __init__(self, base_temp_dir: Optional[str] = None, max_age_hours: int = 24):
        """
        Initialize the temporary file manager.
        
        Args:
            base_temp_dir: Base directory for temporary files. If None, uses system temp.
            max_age_hours: Maximum age of temporary files before cleanup (default: 24 hours)
        """
        self.max_age_hours = max_age_hours
        self.sessions: Dict[str, Dict] = {}  # Track active sessions
        self._cleanup_lock = threading.Lock()
        
        # Set up base temporary directory
        if base_temp_dir:
            self.base_temp_dir = Path(base_temp_dir)
            self.base_temp_dir.mkdir(parents=True, exist_ok=True)
        else:
            self.base_temp_dir = Path(tempfile.gettempdir()) / "translator_bot"
            self.base_temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Register cleanup on exit
        atexit.register(self.cleanup_all)
        
        # Start background cleanup thread
        self._start_cleanup_thread()
    
    def create_session(self, user_id: str, session_id: Optional[str] = None) -> str:
        """
        Create a new temporary session for a user.
        
        Args:
            user_id: User identifier
            session_id: Optional session ID. If None, generates a new UUID.
            
        Returns:
            str: Session ID for the created session
        """
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        # Create session directory
        session_dir = self.base_temp_dir / f"session_{session_id}"
        session_dir.mkdir(parents=True, exist_ok=True)
        
        # Track session metadata
        self.sessions[session_id] = {
            'user_id': user_id,
            'created_at': datetime.now(),
            'last_accessed': datetime.now(),
            'directory': session_dir,
            'files': {}
        }
        
        current_app.logger.info(f"Created temporary session {session_id} for user {user_id}")
        return session_id
    
    def save_file(self, session_id: str, file_obj, original_filename: str, 
                  file_type: Optional[str] = None) -> Tuple[str, str]:
        """
        Save a file to the temporary session directory.
        
        Args:
            session_id: Session identifier
            file_obj: File object to save
            original_filename: Original filename
            file_type: Optional file type identifier
            
        Returns:
            Tuple[str, str]: (file_id, file_path) for the saved file
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.sessions[session_id]
        session['last_accessed'] = datetime.now()
        
        # Generate unique file ID and secure filename
        file_id = str(uuid.uuid4())
        secure_name = secure_filename(original_filename)
        
        # Create file path
        file_path = session['directory'] / f"{file_id}_{secure_name}"
        
        # Save file
        file_obj.seek(0)  # Reset file pointer
        with open(file_path, 'wb') as f:
            shutil.copyfileobj(file_obj, f)
        
        # Track file metadata
        session['files'][file_id] = {
            'original_filename': original_filename,
            'secure_filename': secure_name,
            'file_path': str(file_path),
            'file_type': file_type,
            'created_at': datetime.now(),
            'size': file_path.stat().st_size
        }
        
        current_app.logger.info(f"Saved file {original_filename} as {file_id} in session {session_id}")
        return file_id, str(file_path)
    
    def get_file_path(self, session_id: str, file_id: str) -> Optional[str]:
        """
        Get the file path for a specific file in a session.
        
        Args:
            session_id: Session identifier
            file_id: File identifier
            
        Returns:
            Optional[str]: File path if found, None otherwise
        """
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        session['last_accessed'] = datetime.now()
        
        if file_id not in session['files']:
            return None
        
        file_path = session['files'][file_id]['file_path']
        if os.path.exists(file_path):
            return file_path
        
        return None
    
    def get_session_files(self, session_id: str) -> Dict[str, Dict]:
        """
        Get all files in a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Dict[str, Dict]: Dictionary of file_id -> file_metadata
        """
        if session_id not in self.sessions:
            return {}
        
        session = self.sessions[session_id]
        session['last_accessed'] = datetime.now()
        
        return session['files'].copy()
    
    def delete_file(self, session_id: str, file_id: str) -> bool:
        """
        Delete a specific file from a session.
        
        Args:
            session_id: Session identifier
            file_id: File identifier
            
        Returns:
            bool: True if file was deleted, False otherwise
        """
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        
        if file_id not in session['files']:
            return False
        
        file_path = session['files'][file_id]['file_path']
        
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            del session['files'][file_id]
            current_app.logger.info(f"Deleted file {file_id} from session {session_id}")
            return True
        except Exception as e:
            current_app.logger.error(f"Error deleting file {file_id}: {e}")
            return False

    def delete_session(self, session_id: str) -> bool:
        """
        Delete an entire session and all its files.

        Args:
            session_id: Session identifier

        Returns:
            bool: True if session was deleted, False otherwise
        """
        if session_id not in self.sessions:
            return False

        session = self.sessions[session_id]
        session_dir = session['directory']

        try:
            # Remove all files and directory
            if session_dir.exists():
                shutil.rmtree(session_dir)

            # Remove from tracking
            del self.sessions[session_id]
            current_app.logger.info(f"Deleted session {session_id}")
            return True
        except Exception as e:
            current_app.logger.error(f"Error deleting session {session_id}: {e}")
            return False

    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions based on max_age_hours.

        Returns:
            int: Number of sessions cleaned up
        """
        with self._cleanup_lock:
            current_time = datetime.now()
            expired_sessions = []

            for session_id, session in self.sessions.items():
                age = current_time - session['last_accessed']
                if age > timedelta(hours=self.max_age_hours):
                    expired_sessions.append(session_id)

            cleaned_count = 0
            for session_id in expired_sessions:
                if self.delete_session(session_id):
                    cleaned_count += 1

            if cleaned_count > 0:
                current_app.logger.info(f"Cleaned up {cleaned_count} expired sessions")

            return cleaned_count

    def cleanup_all(self):
        """
        Clean up all sessions and temporary files.
        Called on application shutdown.
        """
        with self._cleanup_lock:
            session_ids = list(self.sessions.keys())
            for session_id in session_ids:
                self.delete_session(session_id)

            # Clean up base directory if empty
            try:
                if self.base_temp_dir.exists() and not any(self.base_temp_dir.iterdir()):
                    self.base_temp_dir.rmdir()
            except Exception as e:
                current_app.logger.error(f"Error cleaning up base temp directory: {e}")

    def get_session_info(self, session_id: str) -> Optional[Dict]:
        """
        Get information about a session.

        Args:
            session_id: Session identifier

        Returns:
            Optional[Dict]: Session information if found, None otherwise
        """
        if session_id not in self.sessions:
            return None

        session = self.sessions[session_id]
        session['last_accessed'] = datetime.now()

        return {
            'session_id': session_id,
            'user_id': session['user_id'],
            'created_at': session['created_at'].isoformat(),
            'last_accessed': session['last_accessed'].isoformat(),
            'file_count': len(session['files']),
            'total_size': sum(f['size'] for f in session['files'].values())
        }

    def _start_cleanup_thread(self):
        """
        Start background thread for periodic cleanup.
        """
        def cleanup_worker():
            while True:
                try:
                    time.sleep(3600)  # Run every hour
                    self.cleanup_expired_sessions()
                except Exception as e:
                    if current_app:
                        current_app.logger.error(f"Error in cleanup thread: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    def get_stats(self) -> Dict:
        """
        Get statistics about the temporary file manager.

        Returns:
            Dict: Statistics including session count, file count, total size
        """
        total_files = 0
        total_size = 0

        for session in self.sessions.values():
            total_files += len(session['files'])
            total_size += sum(f['size'] for f in session['files'].values())

        return {
            'active_sessions': len(self.sessions),
            'total_files': total_files,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'base_temp_dir': str(self.base_temp_dir),
            'max_age_hours': self.max_age_hours
        }


# Global instance for the application
_temp_file_manager = None


def get_temp_file_manager() -> TemporaryFileManager:
    """
    Get the global temporary file manager instance.

    Returns:
        TemporaryFileManager: The global instance
    """
    global _temp_file_manager
    if _temp_file_manager is None:
        # Initialize with default settings
        _temp_file_manager = TemporaryFileManager()
    return _temp_file_manager


def init_temp_file_manager(base_temp_dir: Optional[str] = None, max_age_hours: int = 24):
    """
    Initialize the global temporary file manager with custom settings.

    Args:
        base_temp_dir: Base directory for temporary files
        max_age_hours: Maximum age of temporary files before cleanup
    """
    global _temp_file_manager
    _temp_file_manager = TemporaryFileManager(base_temp_dir, max_age_hours)
