import threading
import time
import schedule
from datetime import datetime
from flask import current_app
from src.backend.blueprints.translator_bot.temp_file_manager import get_temp_file_manager
from config.config import TranslatorBotConfig


class CleanupScheduler:
    """
    Background scheduler for cleaning up expired temporary files and sessions.
    """
    
    def __init__(self):
        self.config = TranslatorBotConfig()
        self.temp_manager = get_temp_file_manager()
        self.scheduler_thread = None
        self.running = False
        
    def start(self):
        """Start the cleanup scheduler."""
        if self.running:
            return
            
        self.running = True
        
        # Schedule cleanup tasks
        cleanup_interval = self.config.cleanup_interval_minutes
        schedule.every(cleanup_interval).minutes.do(self._cleanup_expired_sessions)
        
        # Schedule daily cleanup for orphaned directories
        schedule.every().day.at("02:00").do(self._deep_cleanup)
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        if current_app:
            current_app.logger.info(f"Cleanup scheduler started with {cleanup_interval} minute intervals")
    
    def stop(self):
        """Stop the cleanup scheduler."""
        self.running = False
        schedule.clear()
        
        if current_app:
            current_app.logger.info("Cleanup scheduler stopped")
    
    def _run_scheduler(self):
        """Run the scheduler in a background thread."""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                if current_app:
                    current_app.logger.error(f"Error in cleanup scheduler: {e}")
                time.sleep(60)  # Continue running even if there's an error
    
    def _cleanup_expired_sessions(self):
        """Clean up expired sessions."""
        try:
            cleaned_count = self.temp_manager.cleanup_expired_sessions()
            if current_app and cleaned_count > 0:
                current_app.logger.info(f"Scheduled cleanup: removed {cleaned_count} expired sessions")
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Error during scheduled cleanup: {e}")
    
    def _deep_cleanup(self):
        """Perform deep cleanup of orphaned directories and files."""
        try:
            import os
            import shutil
            from pathlib import Path
            
            base_temp_dir = self.temp_manager.base_temp_dir
            
            if not base_temp_dir.exists():
                return
            
            orphaned_dirs = []
            
            # Find directories that are not tracked in sessions
            for item in base_temp_dir.iterdir():
                if item.is_dir() and item.name.startswith('session_'):
                    session_id = item.name.replace('session_', '')
                    if session_id not in self.temp_manager.sessions:
                        orphaned_dirs.append(item)
            
            # Remove orphaned directories
            removed_count = 0
            for orphaned_dir in orphaned_dirs:
                try:
                    shutil.rmtree(orphaned_dir)
                    removed_count += 1
                except Exception as e:
                    if current_app:
                        current_app.logger.error(f"Error removing orphaned directory {orphaned_dir}: {e}")
            
            if current_app and removed_count > 0:
                current_app.logger.info(f"Deep cleanup: removed {removed_count} orphaned directories")
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Error during deep cleanup: {e}")
    
    def force_cleanup(self):
        """Force immediate cleanup of all expired sessions."""
        try:
            cleaned_count = self.temp_manager.cleanup_expired_sessions()
            if current_app:
                current_app.logger.info(f"Force cleanup: removed {cleaned_count} expired sessions")
            return cleaned_count
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Error during force cleanup: {e}")
            return 0
    
    def get_status(self):
        """Get the status of the cleanup scheduler."""
        return {
            'running': self.running,
            'cleanup_interval_minutes': self.config.cleanup_interval_minutes,
            'max_age_hours': self.config.max_age_hours,
            'next_cleanup': schedule.next_run().isoformat() if schedule.jobs else None,
            'temp_manager_stats': self.temp_manager.get_stats()
        }


# Global scheduler instance
_cleanup_scheduler = None


def get_cleanup_scheduler() -> CleanupScheduler:
    """
    Get the global cleanup scheduler instance.
    
    Returns:
        CleanupScheduler: The global scheduler instance
    """
    global _cleanup_scheduler
    if _cleanup_scheduler is None:
        _cleanup_scheduler = CleanupScheduler()
    return _cleanup_scheduler


def init_cleanup_scheduler():
    """
    Initialize and start the global cleanup scheduler.
    """
    scheduler = get_cleanup_scheduler()
    scheduler.start()


def stop_cleanup_scheduler():
    """
    Stop the global cleanup scheduler.
    """
    global _cleanup_scheduler
    if _cleanup_scheduler:
        _cleanup_scheduler.stop()
        _cleanup_scheduler = None
