/* Translation Tool Styles */

/* Base font family for all elements */
body, html, .translation-container, .card, .form-control, .btn {
    font-family: "Segoe UI", -apple-system, BlinkMacSystemFont, "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

:root{
    /* New color palette from design specs */
    --corporate-primary: #1E1F41;
    --corporate-secondary: #7B869C;
    --corporate-light: #DFE7EA;
    --golden-primary: #7D653F;
    --golden-secondary: #9E8664;
    --golden-light: #BFAF8F;
    --warm-grey-primary: #85827A;
    --warm-grey-secondary: #BFBAB0;
    --warm-grey-light: #DFD5D2;
    --deep-teal-primary: #24639F;
    --deep-teal-secondary: #526D70;
    --deep-teal-light: #BCD4D2;
    --sustainability-primary: #6E826F;
    --sustainability-secondary: #A8B580;
    --sustainability-light: #C4D69A;
}

/* Translation Tool Styles */
.translation-container {
    background: transparent;
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

.translation-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(30, 31, 65, 0.1);
    border: 1px solid rgba(30, 31, 65, 0.08);
    overflow: hidden;
}

.card-header-custom {
    background: linear-gradient(0deg, var(--corporate-primary) 45%, var(--epr-flower-blue) 100%);
    color: white;
    font-weight: 700;
    padding: 2rem;
    border: none;
    position: relative;
    overflow: hidden;
}

.header-icon-topright {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    font-size: 5rem;
    color: #fff;
    background: rgba(30,31,65,0.12);
    border-radius: 12px;
    padding: 0.5rem 0.7rem;
    box-shadow: 0 2px 8px rgba(30,31,65,0.08);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .header-icon-topright {
        top: 1rem;
        right: 1rem;
        font-size: 5rem;
        padding: 0.35rem 0.5rem;
    }
}

.upload-area {
    border: 2px dashed var(--deep-teal-secondary);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--deep-teal-primary);
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.1) 0%, rgba(82, 109, 112, 0.1) 100%);
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: var(--sustainability-primary);
    background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(168, 181, 128, 0.1) 100%);
}

.upload-icon {
    font-size: 3rem;
    color: var(--deep-teal-primary);
    margin-bottom: 1rem;
}

.file-info {
    background: linear-gradient(135deg, rgba(110, 130, 111, 0.1) 0%, rgba(196, 214, 154, 0.1) 100%);
    border: 1px solid var(--sustainability-light);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.language-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin: 1.5rem 0;
}


.excel-options {
    background: linear-gradient(135deg, rgba(125, 102, 63, 0.05) 0%, rgba(191, 175, 143, 0.05) 100%);
    border: 1px solid var(--golden-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.column-checkbox {
    margin: 0.5rem 0;
}

.column-checkbox input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
}


.progress-container {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
    border-radius: 12px;
    border: 1px solid var(--deep-teal-light);
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background-color: var(--warm-grey-light);
}

.progress-bar-custom {
    background: linear-gradient(90deg, var(--deep-teal-primary) 0%, var(--sustainability-primary) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.status-text {
    color: var(--corporate-primary);
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Version display styles */
#runningEnvDisplay {
    font-size: 0.75rem;
    font-weight: bold;
    letter-spacing: 0.5px;
}

#runningEnvDisplay.bg-secondary {
    background-color: var(--epr-spartan-blue) !important;
}

#versionDisplay {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.2s ease;
}

#versionDisplay:hover {
    color: white !important;
    text-decoration: underline !important;
}

/* Environment-specific colors */
#runningEnvDisplay[data-env="LOCAL"] {
    background-color: var(--epr-green) !important;
}

#runningEnvDisplay[data-env="DEVEL"] {
    background-color: var(--epr-gold) !important;
    color: var(--epr-blue) !important;
}

#runningEnvDisplay[data-env="STAGING"] {
    background-color: var(--epr-danger) !important;
}

#runningEnvDisplay[data-env=""] {
    background-color: var(--epr-blue) !important;
}

/* Translator-specific styles */
.translation-container #runningEnvDisplay {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1050;
}

@media (max-width: 768px) {
    .language-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .translation-container {
        padding: 1rem 0;
    }

    .card-header-custom {
        padding: 1.5rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }
}

/* Toggle All Columns Button Styles */
#toggleAllColumns {
    transition: all 0.3s ease;
    border: 2px solid var(--warm-grey-secondary, #BFBAB0);
    color: var(--corporate-primary, #1E1F41);
    background-color: #ffffff;
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 0.9rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-width: 130px;
}

#toggleAllColumns:hover {
    background-color: var(--warm-grey-light, #DFD5D2);
    border-color: var(--corporate-secondary, #7B869C);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

#toggleAllColumns:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#toggleAllColumns i {
    transition: transform 0.2s ease;
    font-size: 1rem;
}

#toggleAllColumns:hover i {
    transform: scale(1.1);
}

/* Excel Options Header */
.excel-options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.excel-options-header h5 {
    margin: 0;
    flex-grow: 1;
}

/* Professional Progress Bar Styles */
.progress-custom {
    height: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    position: relative;
}

.progress-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

.progress-bar-custom {
    background: linear-gradient(135deg, 
        var(--corporate-primary, #1E1F41) 0%,
        #4a5568 25%,
        #2d3748 50%,
        var(--corporate-primary, #1E1F41) 100%);
    border-radius: 10px;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 2px 8px rgba(30, 31, 65, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.progress-bar-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg, 
        rgba(255, 255, 255, 0.25) 0%, 
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 10px 10px 0 0;
}

.status-text {
    font-size: 0.95rem;
    color: #4a5568;
    margin-top: 0.75rem;
    font-weight: 600;
    text-align: left;
    letter-spacing: 0.025em;
}

/* Progress container enhancements */
.progress-container {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-top: 1.5rem;
}

.progress-container .d-flex {
    margin-bottom: 0.75rem;
}

.progress-container .fw-semibold {
    font-size: 1rem;
    color: var(--corporate-primary, #1E1F41);
    font-weight: 600;
}

#progressPercent {
    font-size: 1rem;
    font-weight: 700;
    color: var(--corporate-primary, #1E1F41);
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    min-width: 60px;
    text-align: center;
}

/* Loading Spinner Styles */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-top: 15px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9ecef;
    border-top: 3px solid var(--corporate-primary, #1E1F41);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Download Link Custom Styles */
.download-link-custom {
    background: var(--corporate-primary, #1E1F41);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 0.85rem 2.5rem;
    box-shadow: 0 2px 8px rgba(30, 31, 65, 0.10);
    transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.01em;
}

.download-link-custom:hover, .download-link-custom:focus {
    background: #23244a;
    color: #fff;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(30, 31, 65, 0.16);
    text-decoration: none;
}

.download-link-custom i {
    font-size: 1.3rem;
}

.download-link-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

/* Start Translation Button Custom Style (Deep Teal) */
.translate-link-custom {
    background: var(--deep-teal-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 0.85rem 2.5rem;
    box-shadow: 0 2px 8px rgba(0, 123, 138, 0.10);
    transition: background 0.2s, transform 0.15s, box-shadow 0.15s;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
    letter-spacing: 0.01em;
    opacity: 1;
    cursor: pointer;
}

.translate-link-custom:hover, .translate-link-custom:focus {
    background: var(--deep-teal-primary);
    color: #fff;
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 16px rgba(0, 123, 138, 0.16);
    text-decoration: none;
}

.translate-link-custom:disabled,
.translate-link-custom[disabled] {
    background: var(--warm-grey-primary);
    color: #e0e0e0;
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.translate-link-custom i {
    font-size: 1.3rem;
}
