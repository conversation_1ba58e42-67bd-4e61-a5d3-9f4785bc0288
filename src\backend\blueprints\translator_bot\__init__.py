from flask import Blueprint, current_app
from .translation_routes import translator_bot_routes
from config.config import TranslatorBotConfig

# Create the main blueprint for translation tool
translator_bot = Blueprint('translator_bot', __name__, template_folder='templates')

# Register the sub-blueprints
translator_bot.register_blueprint(translator_bot_routes)

# Initialize temporary file manager and cleanup scheduler
@translator_bot.record_once
def init_temp_file_system(state):
    """Initialize temporary file system when the blueprint is registered."""
    app = state.app

    # Initialize temporary file manager with configuration
    from .temp_file_manager import init_temp_file_manager
    config = TranslatorBotConfig()

    # Get configuration values
    base_temp_dir = config.base_temp_dir
    max_age_hours = config.max_age_hours

    # Initialize the temporary file manager
    init_temp_file_manager(base_temp_dir, max_age_hours)
    app.logger.info(f"Initialized temporary file manager with base_dir={base_temp_dir or 'system temp'}, max_age={max_age_hours}h")

    # Initialize and start the cleanup scheduler
    from .cleanup_scheduler import init_cleanup_scheduler
    init_cleanup_scheduler()
    app.logger.info("Started temporary file cleanup scheduler")

    # Register cleanup on app shutdown
    @app.teardown_appcontext
    def cleanup_on_shutdown(exception=None):
        from .cleanup_scheduler import stop_cleanup_scheduler
        from .temp_file_manager import get_temp_file_manager

        # Stop the cleanup scheduler
        stop_cleanup_scheduler()

        # Clean up all temporary files
        temp_manager = get_temp_file_manager()
        temp_manager.cleanup_all()

        app.logger.info("Cleaned up temporary files on shutdown")