from flask import Blueprint, render_template, request, jsonify, current_app, session
from src.backend.blueprints.auth.decorators import login_epr
from src.backend.blueprints.translator_bot.translation_service import DirectTranslationService
from src.backend.blueprints.translator_bot.temp_file_manager import get_temp_file_manager
from src.backend.models import User
from src import db
import os
import pandas as pd
from werkzeug.utils import secure_filename


translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


@translator_bot_routes.route('/')
@login_epr
def index():
    """Main translation tool page"""
    current_app.logger.info("Translation tool accessed")
    return render_template('translator_bot/translation_tool.html')


@translator_bot_routes.route('/api/upload', methods=['POST'])
@login_epr
def upload_file():
    """Handle file upload for translation using temporary file system"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get or create session ID for this upload
        session_id = request.form.get('session_id')  # Optional session ID from client

        # For Excel files, save and process with temporary file system
        if file_extension == '.xlsx':
            try:
                # Initialize DirectTranslationService with temporary file system
                translation_service = DirectTranslationService(user_id, session_id)

                # Save file to temporary directory
                file_id = translation_service.set_excel_file(file, file.filename)

                # Read Excel file to get column information
                file_path = translation_service.get_translated_file_path()
                df = pd.read_excel(file_path, nrows=5)  # Preview first 5 rows
                columns = df.columns.tolist()

                # Convert preview data safely
                preview_data = []
                for _, row in df.head().iterrows():
                    row_dict = {}
                    for col in columns:
                        value = row[col]
                        # Handle NaN and other non-serializable values
                        if pd.isna(value):
                            row_dict[col] = None
                        elif isinstance(value, (int, float, str, bool)):
                            row_dict[col] = value
                        else:
                            row_dict[col] = str(value)
                    preview_data.append(row_dict)

                # Get total rows safely
                try:
                    total_df = pd.read_excel(file_path)
                    total_rows = len(total_df)
                except:
                    total_rows = len(df)

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'type': file_extension,
                    'columns': columns,
                    'preview': preview_data,
                    'total_rows': total_rows,
                    'session_id': translation_service.get_session_id(),
                    'file_id': file_id
                })
            except Exception as e:
                current_app.logger.error(f"Excel processing error: {e}")
                return jsonify({'error': f'Error reading Excel file: {str(e)}'}), 400

        else:
            # For other file types, use temporary file system
            temp_manager = get_temp_file_manager()
            session_id = temp_manager.create_session(user_id, session_id)
            file_id, file_path = temp_manager.save_file(session_id, file, file.filename, file_extension[1:])

            return jsonify({
                'success': True,
                'filename': file.filename,
                'size': os.path.getsize(file_path),
                'type': file_extension,
                'session_id': session_id,
                'file_id': file_id
            })

    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Handle document translation request using temporary file system"""
    try:
        data = request.get_json()

        # Validate required fields
        if not data or 'target_language' not in data:
            return jsonify({'error': 'Target language is required'}), 400

        target_language = data['target_language']
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        file_type = data.get('file_type', '')
        session_id = data.get('session_id')  # Session ID from upload

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Validate session ID is provided for temporary file system
        if not session_id:
            return jsonify({'error': 'Session ID is required'}), 400

        # Handle Excel files with DirectTranslationService
        if file_type == '.xlsx':
            try:
                # Initialize DirectTranslationService with existing session
                translation_service = DirectTranslationService(user_id, session_id)

                # Verify that a file has been uploaded to this session
                if not translation_service.get_translated_file_path():
                    return jsonify({'error': 'No Excel file found in session. Please upload a file first.'}), 400

                # Translate selected columns or all columns
                if selected_columns:
                    result = translation_service.translate_multiple_columns(
                        selected_columns,
                        target_language,
                        max_rows=200
                    )
                else:
                    # Get all columns and translate them
                    all_columns = translation_service.get_excel_columns()
                    # Filter out non-text columns if needed (you can add logic here)
                    text_columns = [col for col in all_columns if col]  # Simple filter for now
                    result = translation_service.translate_multiple_columns(
                        text_columns,
                        target_language,
                        max_rows=200
                    )

                current_app.logger.info(f"Translation completed for user {user_id}, session {session_id}")

                if result['success']:
                    return jsonify({
                        'success': True,
                        'message': result['message'],
                        'translation_id': f'trans_{session_id}_{hash(target_language)}',
                        'status': 'completed',
                        'data': result,
                        'columns_translated': selected_columns or 'all',
                        'session_id': session_id
                    })
                else:
                    return jsonify({'error': f'Translation failed: {result.get("error", "Unknown error")}'}), 500

            except Exception as e:
                current_app.logger.error(f"Direct translation error: {e}")
                return jsonify({'error': f'Excel translation failed: {str(e)}'}), 500

        else:
            # For other file types, implement other translation logic
            current_app.logger.info(f"Translation request: {source_language} -> {target_language}")

            return jsonify({
                'success': True,
                'message': 'Translation completed for non-Excel file',
                'translation_id': f'trans_{session_id}',
                'status': 'completed',
                'session_id': session_id
            })

    except Exception as e:
        current_app.logger.error(f"Translation error: {e}")
        return jsonify({'error': 'Translation failed'}), 500


@translator_bot_routes.route('/api/status/<translation_id>')
@login_epr
def get_translation_status(translation_id):
    """Get translation status"""
    try:
        # Here you would check actual translation status
        # For now, simulate completion
        return jsonify({
            'success': True,
            'translation_id': translation_id,
            'status': 'completed',
            'progress': 100,
            'download_url': f'/translator/api/download/{translation_id}'
        })
        
    except Exception as e:
        current_app.logger.error(f"Status check error: {e}")
        return jsonify({'error': 'Status check failed'}), 500


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file from temporary file system"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Extract session ID from translation ID
        # Format: trans_<session_id>_<hash>
        parts = translation_id.split('_')
        if len(parts) < 2:
            return jsonify({'error': 'Invalid translation ID format'}), 400

        session_id = parts[1]  # Get session ID from translation ID

        # Initialize DirectTranslationService with the session ID
        translation_service = DirectTranslationService(user_id, session_id)

        # Get the file path from the translation service
        file_path = translation_service.get_translated_file_path()

        if file_path and os.path.exists(file_path):
            # Return the file for download
            from flask import send_file

            # Get original filename from session if available
            temp_manager = get_temp_file_manager()
            session_files = temp_manager.get_session_files(session_id)

            if translation_service.current_file_id and translation_service.current_file_id in session_files:
                original_filename = session_files[translation_service.current_file_id]['original_filename']
                download_name = f"translated_{original_filename}"
            else:
                download_name = f"translated_file_{session_id}.xlsx"

            return send_file(
                file_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            return jsonify({'error': 'Translated file not found'}), 404

    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500


@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Preview translation for Excel files using temporary file system"""
    try:
        data = request.get_json()
        current_app.logger.info(f"Preview request data: {data}")

        if not data or 'column_name' not in data or 'target_language' not in data:
            current_app.logger.error(f"Invalid preview request data: {data}")
            return jsonify({'error': 'Column name and target language are required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        column_name = data['column_name']
        target_language = data['target_language']
        session_id = data.get('session_id')  # Session ID from client

        if not session_id:
            return jsonify({'error': 'Session ID is required'}), 400

        # Initialize DirectTranslationService for preview with session
        translation_service = DirectTranslationService(user_id, session_id)

        # Verify that a file has been uploaded to this session
        if not translation_service.get_translated_file_path():
            return jsonify({'error': 'No Excel file found in session. Please upload a file first.'}), 400

        # Get preview translation
        preview_result = translation_service.preview_translation(
            column_name,
            target_language,
            preview_rows=10
        )

        if preview_result['success']:
            # Create HTML table for preview
            preview_html = "<table class='table table-striped'><thead><tr><th>Row</th><th>Original</th><th>Translated</th></tr></thead><tbody>"
            for item in preview_result['preview_data']:
                preview_html += f"<tr><td>{item['row']}</td><td>{item['original']}</td><td>{item['translated']}</td></tr>"
            preview_html += "</tbody></table>"

            return jsonify({
                'success': True,
                'preview_html': preview_html,
                'total_rows': preview_result['total_rows_in_file'],
                'column': preview_result['column'],
                'target_language': preview_result['target_language'],
                'session_id': session_id
            })
        else:
            return jsonify({'error': preview_result.get('error', 'Preview failed')}), 500

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'error': 'Preview failed'}), 500


@translator_bot_routes.route('/api/columns', methods=['GET'])
@login_epr
def get_excel_columns():
    """Get available columns from uploaded Excel file using temporary file system"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get session ID from query parameter
        session_id = request.args.get('session_id')

        if not session_id:
            return jsonify({'error': 'Session ID is required'}), 400

        # Initialize DirectTranslationService with session
        translation_service = DirectTranslationService(user_id, session_id)

        # Verify that a file has been uploaded to this session
        if not translation_service.get_translated_file_path():
            return jsonify({'error': 'No Excel file found in session. Please upload a file first.'}), 400

        # Get columns from Excel file
        columns = translation_service.get_excel_columns()

        if columns:
            return jsonify({
                'success': True,
                'columns': columns,
                'session_id': session_id
            })
        else:
            return jsonify({'error': 'No columns found or file not uploaded'}), 404

    except Exception as e:
        current_app.logger.error(f"Get columns error: {e}")
        return jsonify({'error': 'Failed to get columns'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/session/cleanup', methods=['POST'])
@login_epr
def cleanup_session():
    """Clean up a specific session and its files"""
    try:
        data = request.get_json()
        session_id = data.get('session_id') if data else None

        if not session_id:
            return jsonify({'error': 'Session ID is required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get temp file manager and verify session belongs to user
        temp_manager = get_temp_file_manager()
        session_info = temp_manager.get_session_info(session_id)

        if not session_info:
            return jsonify({'error': 'Session not found'}), 404

        if session_info['user_id'] != user_id:
            return jsonify({'error': 'Unauthorized access to session'}), 403

        # Delete the session
        if temp_manager.delete_session(session_id):
            return jsonify({
                'success': True,
                'message': f'Session {session_id} cleaned up successfully'
            })
        else:
            return jsonify({'error': 'Failed to clean up session'}), 500

    except Exception as e:
        current_app.logger.error(f"Session cleanup error: {e}")
        return jsonify({'error': 'Session cleanup failed'}), 500


@translator_bot_routes.route('/api/session/info', methods=['GET'])
@login_epr
def get_session_info():
    """Get information about a session"""
    try:
        session_id = request.args.get('session_id')

        if not session_id:
            return jsonify({'error': 'Session ID is required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Get temp file manager and verify session belongs to user
        temp_manager = get_temp_file_manager()
        session_info = temp_manager.get_session_info(session_id)

        if not session_info:
            return jsonify({'error': 'Session not found'}), 404

        if session_info['user_id'] != user_id:
            return jsonify({'error': 'Unauthorized access to session'}), 403

        return jsonify({
            'success': True,
            'session_info': session_info
        })

    except Exception as e:
        current_app.logger.error(f"Get session info error: {e}")
        return jsonify({'error': 'Failed to get session info'}), 500


@translator_bot_routes.route('/api/temp-manager/stats', methods=['GET'])
@login_epr
def get_temp_manager_stats():
    """Get statistics about the temporary file manager (admin only)"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401

        # You might want to add admin role check here
        # if not user.get('is_admin'):
        #     return jsonify({'error': 'Admin access required'}), 403

        temp_manager = get_temp_file_manager()
        stats = temp_manager.get_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        current_app.logger.error(f"Get temp manager stats error: {e}")
        return jsonify({'error': 'Failed to get stats'}), 500


@translator_bot_routes.route('/api/cleanup/status', methods=['GET'])
@login_epr
def get_cleanup_status():
    """Get status of the cleanup scheduler"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401

        from .cleanup_scheduler import get_cleanup_scheduler
        scheduler = get_cleanup_scheduler()
        status = scheduler.get_status()

        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        current_app.logger.error(f"Get cleanup status error: {e}")
        return jsonify({'error': 'Failed to get cleanup status'}), 500


@translator_bot_routes.route('/api/cleanup/force', methods=['POST'])
@login_epr
def force_cleanup():
    """Force immediate cleanup of expired sessions"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401

        # You might want to add admin role check here
        # if not user.get('is_admin'):
        #     return jsonify({'error': 'Admin access required'}), 403

        from .cleanup_scheduler import get_cleanup_scheduler
        scheduler = get_cleanup_scheduler()
        cleaned_count = scheduler.force_cleanup()

        return jsonify({
            'success': True,
            'message': f'Cleaned up {cleaned_count} expired sessions',
            'cleaned_count': cleaned_count
        })

    except Exception as e:
        current_app.logger.error(f"Force cleanup error: {e}")
        return jsonify({'error': 'Force cleanup failed'}), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os

        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500
