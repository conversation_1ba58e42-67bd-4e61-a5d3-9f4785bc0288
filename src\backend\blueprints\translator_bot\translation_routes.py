from flask import Blueprint, render_template, request, jsonify, current_app, session
from src.backend.blueprints.auth.decorators import login_epr
from src.backend.blueprints.translator_bot.translation_service import DirectTranslationService
from src.backend.models import User
from src import db
import os
import pandas as pd
from werkzeug.utils import secure_filename


translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


@translator_bot_routes.route('/')
@login_epr
def index():
    """Main translation tool page"""
    current_app.logger.info("Translation tool accessed")
    return render_template('translator_bot/translation_tool.html')


@translator_bot_routes.route('/api/upload', methods=['POST'])
@login_epr
def upload_file():
    """Handle file upload for translation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # For Excel files, save and process with EPROExcelLa
        if file_extension == '.xlsx':
            # Create upload directory if it doesn't exist
            upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'agents', 'eproexcella_agent', 'upload')
            os.makedirs(upload_dir, exist_ok=True)
            
            # Save file with user ID
            filename = secure_filename(f"data_{user_id}.xlsx")
            file_path = os.path.join(upload_dir, filename)
            file.seek(0)  # Reset file pointer after reading
            file.save(file_path)
            
            # Read Excel file to get column information
            try:
                df = pd.read_excel(file_path, nrows=5)  # Preview first 5 rows
                columns = df.columns.tolist()

                # Convert preview data safely
                preview_data = []
                for _, row in df.head().iterrows():
                    row_dict = {}
                    for col in columns:
                        value = row[col]
                        # Handle NaN and other non-serializable values
                        if pd.isna(value):
                            row_dict[col] = None
                        elif isinstance(value, (int, float, str, bool)):
                            row_dict[col] = value
                        else:
                            row_dict[col] = str(value)
                    preview_data.append(row_dict)

                # Get total rows safely
                try:
                    total_df = pd.read_excel(file_path)
                    total_rows = len(total_df)
                except:
                    total_rows = len(df)

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'type': file_extension,
                    'columns': columns,
                    'preview': preview_data,
                    'total_rows': total_rows
                })
            except Exception as e:
                current_app.logger.error(f"Excel processing error: {e}")
                return jsonify({'error': f'Error reading Excel file: {str(e)}'}), 400
        
        else:
            # For other file types, use existing logic
            return jsonify({
                'success': True,
                'filename': file.filename,
                'size': len(file.read()),
                'type': file_extension
            })
        
    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Handle document translation request"""
    try:
        data = request.get_json()

        # Validate required fields
        if not data or 'target_language' not in data:
            return jsonify({'error': 'Target language is required'}), 400

        target_language = data['target_language']
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        file_type = data.get('file_type', '')

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # Handle Excel files with DirectTranslationService
        if file_type == '.xlsx':
            try:
                # Initialize DirectTranslationService
                translation_service = DirectTranslationService(user_id)

                # Translate selected columns or all columns
                if selected_columns:
                    result = translation_service.translate_multiple_columns(
                        selected_columns,
                        target_language,
                        max_rows=200
                    )
                else:
                    # Get all columns and translate them
                    all_columns = translation_service.get_excel_columns()
                    # Filter out non-text columns if needed (you can add logic here)
                    text_columns = [col for col in all_columns if col]  # Simple filter for now
                    result = translation_service.translate_multiple_columns(
                        text_columns,
                        target_language,
                        max_rows=200
                    )

                current_app.logger.info(f"Translation completed for user {user_id}")

                if result['success']:
                    return jsonify({
                        'success': True,
                        'message': result['message'],
                        'translation_id': f'trans_{user_id}_{hash(target_language)}',
                        'status': 'completed',
                        'data': result,
                        'columns_translated': selected_columns or 'all'
                    })
                else:
                    return jsonify({'error': f'Translation failed: {result.get("error", "Unknown error")}'}), 500

            except Exception as e:
                current_app.logger.error(f"Direct translation error: {e}")
                return jsonify({'error': f'Excel translation failed: {str(e)}'}), 500
        
        else:
            # For other file types, implement other translation logic
            current_app.logger.info(f"Translation request: {source_language} -> {target_language}")
            
            return jsonify({
                'success': True,
                'message': 'temp',
                'translation_id': 'temp',
                'status': 'temp'
            })
        
    except Exception as e:
        current_app.logger.error(f"Translation error: {e}")
        return jsonify({'error': 'Translation failed'}), 500


@translator_bot_routes.route('/api/status/<translation_id>')
@login_epr
def get_translation_status(translation_id):
    """Get translation status"""
    try:
        # Here you would check actual translation status
        # For now, simulate completion
        return jsonify({
            'success': True,
            'translation_id': translation_id,
            'status': 'completed',
            'progress': 100,
            'download_url': f'/translator/api/download/{translation_id}'
        })
        
    except Exception as e:
        current_app.logger.error(f"Status check error: {e}")
        return jsonify({'error': 'Status check failed'}), 500


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # For Excel files, get the processed file from DirectTranslationService
        upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'agents', 'eproexcella_agent', 'upload')
        file_path = os.path.join(upload_dir, f"data_{user_id}.xlsx")

        if os.path.exists(file_path):
            # Return the file for download
            from flask import send_file
            return send_file(
                file_path,
                as_attachment=True,
                download_name=f"translated_file_{user_id}.xlsx",
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            return jsonify({'error': 'Translated file not found'}), 404
        
    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500


@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Preview translation for Excel files"""
    try:
        data = request.get_json()
        current_app.logger.info(f"Preview request data: {data}")

        if not data or 'column_name' not in data or 'target_language' not in data:
            current_app.logger.error(f"Invalid preview request data: {data}")
            return jsonify({'error': 'Column name and target language are required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        column_name = data['column_name']
        target_language = data['target_language']

        # Initialize DirectTranslationService for preview
        translation_service = DirectTranslationService(user_id)

        # Get preview translation
        preview_result = translation_service.preview_translation(
            column_name,
            target_language,
            preview_rows=10
        )

        if preview_result['success']:
            # Create HTML table for preview
            preview_html = "<table class='table table-striped'><thead><tr><th>Row</th><th>Original</th><th>Translated</th></tr></thead><tbody>"
            for item in preview_result['preview_data']:
                preview_html += f"<tr><td>{item['row']}</td><td>{item['original']}</td><td>{item['translated']}</td></tr>"
            preview_html += "</tbody></table>"

            return jsonify({
                'success': True,
                'preview_html': preview_html,
                'total_rows': preview_result['total_rows_in_file'],
                'column': preview_result['column'],
                'target_language': preview_result['target_language']
            })
        else:
            return jsonify({'error': preview_result.get('error', 'Preview failed')}), 500

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'error': 'Preview failed'}), 500


@translator_bot_routes.route('/api/columns', methods=['GET'])
@login_epr
def get_excel_columns():
    """Get available columns from uploaded Excel file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Initialize DirectTranslationService
        translation_service = DirectTranslationService(user_id)

        # Get columns from Excel file
        columns = translation_service.get_excel_columns()

        if columns:
            return jsonify({
                'success': True,
                'columns': columns
            })
        else:
            return jsonify({'error': 'No columns found or file not uploaded'}), 404

    except Exception as e:
        current_app.logger.error(f"Get columns error: {e}")
        return jsonify({'error': 'Failed to get columns'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os
        
        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500
